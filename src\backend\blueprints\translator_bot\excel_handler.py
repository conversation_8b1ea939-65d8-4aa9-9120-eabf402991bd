import json
import pandas as pd
from openpyxl import load_workbook
import os
import shutil
from typing import List, Dict, Tuple, Callable
from flask import current_app


class ExcelHandler:
    """
    Handles Excel file operations for the translation service.
    Separated from the main translation service to focus on Excel-specific functionality.
    """

    def __init__(self, excel_path: str):
        """
        Initialize the Excel handler with the path to the Excel file.
        
        Args:
            excel_path (str): Path to the Excel file
        """
        self.excel_path = excel_path


    def get_batches(self, selected_columns=None, max_rows=200):
        """
        Generate JSON batches for selected columns, each batch with up to max_rows items.
        Args:
            selected_columns (list): List of column names to process. If None, use all columns.
            max_rows (int): Maximum number of rows per batch.
        Returns:
            List[Dict]: List of JSON batch objects for translation.
        """
        df = pd.read_excel(self.excel_path)
        columns = selected_columns if selected_columns else df.columns.tolist()
        batches = []
        for col in columns:
            col_data = df[col].fillna("").astype(str)
            for i in range(0, len(col_data), max_rows):
                chunk = col_data.iloc[i:i+max_rows]
                json_obj = {
                    "column": col,
                    "content": {str(j+1+i): value for j, value in enumerate(chunk)}
                }
                batches.append(json_obj)
        return batches

    
    def write_results_to_file(self, results: List[str], lang: str, header: str = None, selected_columns: List[str] = None):
        """
        Write the results to new columns in the Excel file.

        Args:
            results (List[str]): List of results to write (each result corresponds to a batch)
            lang (str): Target language code
            header (str, optional): Header value for the output column
            selected_columns (List[str], optional): List of selected columns to match with results
        """
        current_app.logger.info(f"Writing results to Excel for language: {lang}, header: {header}")

        # Load the workbook and get the active sheet
        workbook = load_workbook(self.excel_path)
        sheet = workbook.active

        # Get the original DataFrame to determine starting column index
        df = pd.read_excel(self.excel_path)
        starting_col_idx = len(df.columns) + 1

        # Get all batches to understand the structure
        all_columns = selected_columns if selected_columns else df.columns.tolist()
        batches = self.get_batches(all_columns)
        
        if len(results) != len(batches):
            current_app.logger.warning(f"Mismatch between results ({len(results)}) and batches ({len(batches)})")

        # Group results by column name to handle multiple columns
        column_results = {}
        for i, result in enumerate(results):
            try:
                # Parse the result as JSON to extract the values
                result_json = json.loads(result)
                current_app.logger.debug(f"Processing result batch {i+1}: {len(result_json)} items")

                # Get column name from corresponding batch
                if i < len(batches):
                    column_name = batches[i].get('column', f'Column_{i}')
                else:
                    column_name = f'Column_{i}'

                if column_name not in column_results:
                    column_results[column_name] = {}

                # Merge the results for this column
                for row_idx, value in result_json.items():
                    column_results[column_name][row_idx] = value

            except json.JSONDecodeError as e:
                current_app.logger.warning(f"Invalid JSON in result batch {i+1}: {e}")
                # If the result is not valid JSON, treat as a single value
                if i < len(batches):
                    column_name = batches[i].get('column', f'Column_{i}')
                else:
                    column_name = f'Column_{i}'
                if column_name not in column_results:
                    column_results[column_name] = {}
                column_results[column_name][str(i+1)] = result
            except Exception as e:
                current_app.logger.error(f"Error processing result batch {i+1}: {e}")

        # Write each column's results to a separate Excel column
        current_col_idx = starting_col_idx
        total_values_written = 0

        for column_name, column_data in column_results.items():
            # Write the header first
            header_text = f"{column_name}_{lang}_translated" if header is None else f"{header}_{lang}_translated"
            sheet.cell(row=1, column=current_col_idx, value=header_text)
            current_app.logger.info(f"Written header '{header_text}' to column {current_col_idx}")

            # Write the results for this column
            for row_idx, value in column_data.items():
                actual_row = int(row_idx) + 1  # +1 for header row
                sheet.cell(row=actual_row, column=current_col_idx, value=value)
                total_values_written += 1

            current_app.logger.info(f"Written {len(column_data)} values for column '{column_name}' to Excel column {current_col_idx}")
            current_col_idx += 1

        current_app.logger.info(f"Written {total_values_written} total values across {len(column_results)} columns")

        # Save the workbook to a new file for the language
        import os
        original_base, original_ext = os.path.splitext(self.excel_path)
        translated_path = f"{original_base}_{lang}{original_ext}"
        try:
            workbook.save(translated_path)
            current_app.logger.info(f"Successfully saved translated Excel file: {translated_path}")
        except Exception as e:
            current_app.logger.error(f"Error saving translated Excel file: {e}")
            raise


    def get_excel_info(self) -> Dict:
        """
        Get Excel file information including columns and row count.

        Returns:
            Dict: Dictionary containing columns list and row count
        """
        try:
            df = pd.read_excel(self.excel_path)
            return {
                'col_names': df.columns.tolist(),
                'col_count': len(df.columns),
                'row_count': len(df),
            }
        except Exception as e:
            current_app.logger.error(f"Error reading Excel info: {e}")
            return {
                'col_names': [],
                'col_count': 0,
                'row_count': 0,
                'error': str(e)
            }

    def get_next_available_column_letter(self) -> str:
        """
        Get the next available column letter for writing results.

        Returns:
            str: Next available column letter (e.g., 'C', 'D', etc.)
        """
        try:
            df = pd.read_excel(self.excel_path)
            return chr(ord('A') + len(df.columns))
        except Exception as e:
            current_app.logger.error(f"Error determining next column: {e}")
            return 'B'  # Default fallback
    
    
    def get_total_batches(self, selected_columns, target_languages, max_rows=200):
        excel_info = self.get_excel_info()
        columns = len(selected_columns) if selected_columns else len(excel_info.get('col_names', []))
        if target_languages and isinstance(target_languages, list) and len(target_languages):
            return len(target_languages)
        else:
            return columns