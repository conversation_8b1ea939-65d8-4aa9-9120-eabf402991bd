<!-- templates/translator_bot/translation_tool.html -->

{% extends "base.html" %}

{% block head_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='translator_tool.css') }}">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="translation-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="translation-card">
                    <div class="card-header-custom position-relative text-center" style="display: flex; flex-direction: column; align-items: center; padding-top: 0;">
                        <span class="header-icon-top" style="font-size: 5rem; margin-bottom: 0.1rem; margin-top: 0; display: block;">
                            <i class="fas fa-language"></i>
                        </span>
                        <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 700;">Document Translation Tool</h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Translate Excel documents with AI
                        </p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- File Upload Section -->
                        <div class="mb-4 mt-4">                            
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h6>Drag & drop your file here</h6>
                                <p class="text-muted mb-3">or click to browse</p>
                                <p class="small text-muted">
                                    Supported formats: Excel (.xlsx), PowerPoint (.pptx), Word (.docx)
                                    <br>Maximum file size: 50MB
                                </p>
                                <input type="file" id="fileInput" accept=".xlsx,.pptx,.docx" style="display: none;">
                            </div>
                            
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file me-3 text-dark"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold" id="fileName"></div>
                                        <div class="small text-muted" id="fileDetails"></div>
                                    </div>
                                    <button class="remove-file-btn" id="removeFile" type="button">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Loading Spinner -->
                            <div id="loadingSpinner" class="loading-spinner">
                                <div class="spinner"></div>
                                <div class="loading-text" id="loadingText">Uploading and processing file...</div>
                            </div>
                        </div>

                        <!-- File Options (Excel columns, Word paragraphs, PPTX slides) -->
                        <div id="fileOptions" class="excel-options" style="display: none;">
                            <div class="excel-options-header">
                                <h5 id="fileOptionsHeader"></h5>
                                <button type="button" class="select-all-btn" id="toggleAllColumns" style="display:none;"><i class="fas fa-check-square me-1"></i>Deselect All</button>
                            </div>
                            <p class="text-muted mb-3" id="fileOptionsDescription"></p>
                            <div class="row" id="optionCheckboxes">
                                <!-- Checkboxes will be populated dynamically -->
                            </div>
                        </div>

                        <!-- File Context Input & Language Selection -->
                        <div id="languageSettings" class="excel-options mt-4" style="display: none;">
                            <div class="excel-options-header">
                                <h5>
                                    <i class="fas fa-globe me-2"></i>
                                    Language Settings
                                </h5>
                            </div>
                            <!-- Context Input -->
                            <div class="mb-3">
                                <label for="fileContext" class="form-label fw-semibold">File Context (optional)</label>
                                <textarea class="form-control" id="fileContext" rows="3" placeholder="Add any relevant context or instructions for the model to improve the translation accuracy."></textarea>
                            </div>
                            <div class="language-grid">
                                <div>
                                    <label for="sourceLanguage" class="form-label fw-semibold">Source Language</label>
                                    <select class="form-select form-select-custom" id="sourceLanguage">
                                        <option value="auto">Auto-detect</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="targetLanguage" class="form-label fw-semibold">Target Language(s)</label>
                                    <select class="form-select form-select-custom" id="targetLanguage" multiple="multiple" style="width: 100%;">
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                        <option value="ar">Arabic</option>
                                        <option value="hi">Hindi</option>
                                        <option value="th">Thai</option>
                                        <option value="vi">Vietnamese</option>
                                        <option value="nl">Dutch</option>
                                        <option value="sv">Swedish</option>
                                        <option value="no">Norwegian</option>
                                        <option value="da">Danish</option>
                                        <option value="fi">Finnish</option>
                                        <option value="pl">Polish</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Translation Button -->

                        <div class="text-center mt-4">
                            <button class="translate-link-custom btn-lg" id="translateBtn" disabled>
                                <i class="fas fa-magic me-2"></i>
                                Start Translation
                            </button>
                        </div>

                        <!-- Progress Bar -->
                        <div id="progressContainer" class="mt-4" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-semibold">Translation Progress</span>
                                <span id="progressText" class="text-muted">0%</span>
                            </div>
                            <div class="progress" style="height: 12px;">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%;"
                                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            <div id="progressDetails" class="small text-muted mt-2"></div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for targetLanguage
    $('#targetLanguage').select2({
        placeholder: 'Select target language(s)',
        allowClear: true,
        width: 'resolve'
    });


    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileDetails = document.getElementById('fileDetails');
    const removeFile = document.getElementById('removeFile');
    const fileOptions = document.getElementById('fileOptions');
    const optionCheckboxes = document.getElementById('optionCheckboxes');
    const toggleAllColumns = document.getElementById('toggleAllColumns');
    const translateBtn = document.getElementById('translateBtn');
    const targetLanguage = document.getElementById('targetLanguage');

    const loadingSpinner = document.getElementById('loadingSpinner');
    const loadingText = document.getElementById('loadingText');
    const languageSettings = document.getElementById('languageSettings');
    
    let selectedFile = null;
    let excelRowCount = 0; // Store the actual row count from Excel file
    let fileType = null;

    // Ensure the translate button is always disabled on page load
    translateBtn.hidden = true;
    // Helper function to show alerts
    function showAlert(message, type = 'info') {
        // Create a simple alert div
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // File input change handler
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // Remove file handler
    removeFile.addEventListener('click', () => {
        selectedFile = null;
        translationCompleted = false;
        excelRowCount = 0;
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'block';
        fileOptions.style.display = 'none';
        loadingSpinner.style.display = 'none';
        fileInput.value = '';
        languageSettings.style.display = 'none';
        const existingDownloadLinks = document.querySelectorAll('.download-link-custom');
        existingDownloadLinks.forEach(link => link.remove());
        updateTranslateButton();
    });

    // Target language change handler (Select2 event)
    $('#targetLanguage').on('change', function() {
        updateTranslateButton();
        showTranslateButtonAfterChange();
    });

    // Update button state when columns are selected/deselected
    optionCheckboxes.addEventListener('change', function() {
        updateTranslateButton();
        showTranslateButtonAfterChange();
    });

    // Context change handler
    const fileContextTextarea = document.getElementById('fileContext');
    fileContextTextarea.addEventListener('input', showTranslateButtonAfterChange);

    // Source language change handler
    const sourceLanguageSelect = document.getElementById('sourceLanguage');
    sourceLanguageSelect.addEventListener('change', showTranslateButtonAfterChange);

    // Translate button handler
    translateBtn.addEventListener('click', startTranslation);

    // Toggle all columns button handler
    toggleAllColumns.addEventListener('click', function() {
        const checkboxContainer = document.getElementById('columnCheckboxes');
        if (!checkboxContainer) return;
        const checkboxes = checkboxContainer.querySelectorAll('input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        // Update button text and icon safely
        let icon = this.querySelector('i');
        if (allChecked) {
            this.innerHTML = '<i class="fas fa-square me-1"></i>Select All';
        } else {
            this.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';
        }
        updateTranslateButton();
    });

    function handleFileSelect(file) {
        // Automatically detect file type by extension
        const allowedTypes = ['.xlsx', '.pptx', '.docx'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        fileType = fileExtension;
        if (!allowedTypes.includes(fileExtension)) {
            showAlert('Please select a valid file type: Excel (.xlsx), PowerPoint (.pptx), or Word (.docx)', 'warning');
            return;
        }
        if (file.size > 50 * 1024 * 1024) {
            showAlert('File size must be less than 50MB', 'warning');
            return;
        }
        selectedFile = file;
        translationCompleted = false;
        excelRowCount = 0;
        languageSettings.style.display = 'none';
        uploadArea.style.display = 'none';
        loadingSpinner.style.display = 'block';
        loadingText.textContent = 'Uploading and processing file...';
        // All logic is now automatic based on file extension
        if (fileExtension === '.xlsx') {
            uploadFileToServer(file);
        } else {
            setTimeout(() => {
                loadingSpinner.style.display = 'none';
                fileName.textContent = file.name;
                fileDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB • ${fileExtension.toUpperCase().substring(1)} file`;
                fileInfo.style.display = 'block';
                showFileOptions(fileExtension);
                updateTranslateButton();
            }, 800);
        }
    }

    function uploadFileToServer(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        loadingText.textContent = 'Uploading file...';

        fetch('/translator/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.type == '.xlsx') {
                loadingText.textContent = 'Getting Excel table...';
                getExcelColumns();
                }
                else if (data.type == '.pptx') {
                loadingText.textContent = 'Getting PowerPoint slides...';
                // TODO
                }
                else if (data.type == '.docx') {
                loadingText.textContent = 'Getting Word paragraphs...';
                // TODO
                }
                finishFileLoading(data.type);
            } else {
                loadingSpinner.style.display = 'none';
                uploadArea.style.display = 'block';
                alert('Error uploading file: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            alert('Error uploading file');
        });
    }

    function finishFileLoading(fileExtension) {
        // Hide loading spinner and show file info
        loadingSpinner.style.display = 'none';
        // Show language settings now that upload/processing is done
        languageSettings.style.display = 'block';
        // Update file info display
        fileName.textContent = selectedFile.name;
        fileInfo.style.display = 'block';
        updateTranslateButton();
    }

    function getExcelColumns() {
        loadingText.textContent = 'Analyzing Excel columns...';
        
        fetch('/translator/api/columns', {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                excelRowCount = data.row_count || 0;
                console.log(`Excel file has ${excelRowCount} rows`);

                // Use generic file options function for all file types
                showFileOptions('.xlsx', data.columns);
                finishFileLoading();
            } else {
                console.error('Error getting columns:', data.error);
                console.log(data);
                loadingSpinner.style.display = 'none';
                uploadArea.style.display = 'block';
                alert('Error analyzing Excel file: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Get columns error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            alert('Error analyzing Excel file');
        });
    }

    function showFileOptions(fileExtension, options = null) {
        const fileOptionsHeader = document.getElementById('fileOptionsHeader');
        const fileOptionsDescription = document.getElementById('fileOptionsDescription');
        optionCheckboxes.innerHTML = '';
        let opts = [];
        if (fileExtension === '.xlsx') {
            fileOptionsHeader.textContent = 'Select columns to translate:';
            fileOptionsDescription.textContent = 'Select which columns you want to translate.';
            opts = options || [];
            toggleAllColumns.style.display = opts.length > 1 ? 'inline-block' : 'none';
        } else if (fileExtension === '.docx') {
            fileOptionsHeader.textContent = 'Select paragraphs to translate:';
            fileOptionsDescription.textContent = 'Select which paragraphs you want to translate.';
            opts = ['All Paragraphs'];
            toggleAllColumns.style.display = 'none';
        } else if (fileExtension === '.pptx') {
            fileOptionsHeader.textContent = 'Select slides to translate:';
            fileOptionsDescription.textContent = 'Select which slides you want to translate.';
            opts = ['All Slides'];
            toggleAllColumns.style.display = 'none';
        }
        const checkboxContainer = document.createElement('div');
        checkboxContainer.className = 'row';
        checkboxContainer.id = 'columnCheckboxes'; // Assign id for later reference
        opts.forEach((option, index) => {
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-6 column-checkbox';
            colDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${option}" id="opt${index}" checked>
                    <label class="form-check-label" for="opt${index}">
                        ${option}
                    </label>
                </div>
            `;
            checkboxContainer.appendChild(colDiv);
        });
        optionCheckboxes.appendChild(checkboxContainer);
        fileOptions.style.display = 'block';
        languageSettings.style.display = 'block';
        translateBtn.hidden = false;
    }
    
    function showTranslationPreview() {
        if (!selectedFile || !targetLanguage.value) {
            alert('Please select a file and target language');
            return;
        }

        // Get selected columns
        const checkboxContainer = document.getElementById('columnCheckboxes');
        if (!checkboxContainer) {
            alert('No columns found');
            return;
        }
        const checkboxes = checkboxContainer.querySelectorAll('input[type="checkbox"]:checked');
        const selectedColumns = Array.from(checkboxes).map(cb => cb.value);

        if (selectedColumns.length === 0) {
            alert('Please select at least one column to translate');
            return;
        }

        // For now, preview only the first selected column
        const columnToPreview = selectedColumns[0];

        // Call preview API with new format
        const fileContext = document.getElementById('fileContext').value;
        fetch('/translator/api/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                column_name: columnToPreview,
                target_language: getLanguageName(targetLanguage.value),
                file_context: fileContext
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                alert('Preview failed: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Preview error:', error);
            alert('Preview failed');
        });
    }
    
    function getLanguageName(code) {
        const languages = {
            'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
            'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
            'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
            'th': 'Thai', 'vi': 'Vietnamese', 'nl': 'Dutch', 'sv': 'Swedish',
            'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish', 'pl': 'Polish'
        };
        return languages[code] || code;
    }

    function getSelectedTargetLanguages() {
        // Returns an array of selected language codes from Select2
        return $('#targetLanguage').val() || [];
    }

    let translationCompleted = false; // Track if translation was completed

    function updateTranslateButton() {
        const hasFile = selectedFile !== null;
        const selectedLangs = getSelectedTargetLanguages();

        // Hide button if translation was completed and no changes made
        if (translationCompleted) {
            translateBtn.style.display = 'none';
            return;
        }

        translateBtn.hidden = !(hasFile);
        translateBtn.disabled = !(hasFile && selectedLangs.length > 0);
        translateBtn.style.display = hasFile ? 'inline-block' : 'none';
    }

    function showTranslateButtonAfterChange() {
        // Show translate button again when user makes changes
        if (translationCompleted) {
            translationCompleted = false;
            translateBtn.style.display = 'inline-block';
            updateTranslateButton();
        }
    }

    function calculateTotalBatches(selectedColumns, selectedLangs) {
        if (!selectedFile) return 1;
        if (fileType === '.xlsx') {
            const actualRows = excelRowCount || 1000;
            const batchSize = 200;
            const batchesPerColumn = Math.ceil(actualRows / batchSize);
            return selectedColumns.length * selectedLangs.length * batchesPerColumn;
        }
        // For Word and PPTX, just 1 batch for prototype
        return selectedLangs.length;
    }

    function showProgressBar(totalBatches) {
        const progressContainer = document.getElementById('progressContainer');
        const progressText = document.getElementById('progressText');
        const progressBar = document.getElementById('progressBar');
        const progressDetails = document.getElementById('progressDetails');

        progressContainer.style.display = 'block';
        progressText.textContent = '0%';
        progressBar.style.width = '0%';
        progressBar.setAttribute('aria-valuenow', '0');
        progressDetails.textContent = 'Starting translation...';
    }

    function updateProgress(completed, total, details = '') {
        const progressText = document.getElementById('progressText');
        const progressBar = document.getElementById('progressBar');
        const progressDetails = document.getElementById('progressDetails');

        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

        // Update text to show percentage and batch info
        progressText.textContent = `${percentage}%`;

        // Update progress bar width and ensure proper styling
        progressBar.style.width = `${percentage}%`;
        progressBar.style.background = 'linear-gradient(90deg, #24639F, #20b2aa)'; // Deep teal gradient
        progressBar.setAttribute('aria-valuenow', percentage.toString());

        if (details) {
            progressDetails.textContent = details;
        }
    }

    function hideProgressBar() {
        const progressContainer = document.getElementById('progressContainer');
        progressContainer.style.display = 'none';
    }

    function pollProgress(sessionId) {
        return fetch(`/translator/api/progress/${sessionId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Progress data:', data); // Debug log
                if (data.success) {
                    updateProgress(data.completed, data.total, data.details);
                    return data.completed >= data.total; // Return true if complete
                } else {
                    console.log('Progress polling failed:', data.error);
                }
                return false;
            })
            .catch(error => {
                console.error('Progress polling error:', error);
                return false;
            });
    }

    function startTranslation() {
        const selectedLangs = getSelectedTargetLanguages();
        if (!selectedFile || selectedLangs.length === 0) {
            alert('Please select a file and at least one target language');
            return;
        }
        // Get selected columns for Excel files
        let selectedColumns = [];
        if (fileType === '.xlsx') {
            const checkboxes = document.getElementById('columnCheckboxes').querySelectorAll('input[type="checkbox"]:checked');
            selectedColumns = Array.from(checkboxes).map(cb => cb.value);
        } else if (fileType === '.docx') {
            selectedColumns = ['All Paragraphs'];
        } else if (fileType === '.pptx') {
            selectedColumns = ['All Slides'];
        }

        translateBtn.disabled = true;
        translateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Translating...';

        // Calculate and show progress bar
        const estimatedTotalBatches = calculateTotalBatches(selectedColumns, selectedLangs);
        showProgressBar(estimatedTotalBatches);

        // Generate session ID for progress tracking
        const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // Start translation
        const fileContext = document.getElementById('fileContext').value;
        const translationData = {
            target_languages: selectedLangs,
            source_language: document.getElementById('sourceLanguage').value,
            selected_columns: selectedColumns,
            file_type: '.' + selectedFile.name.split('.').pop().toLowerCase(),
            original_filename: selectedFile.name,
            file_context: fileContext,
            session_id: sessionId
        };

        // Start progress polling immediately
        let progressInterval = setInterval(async () => {
            const isComplete = await pollProgress(sessionId);
            if (isComplete) {
                clearInterval(progressInterval);
            }
        }, 1000); // Poll every second

        // Start the translation request
        fetch('/translator/api/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(translationData)
        })
        .then(response => response.json())
        .then(data => {

            translateBtn.disabled = false;
            translateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Start Translation';
            hideProgressBar();

            if (data.success) {
                // Mark translation as completed and hide translate button
                translationCompleted = true;
                translateBtn.style.display = 'none';

                // Show success message
                showAlert('Translation completed successfully!', 'success');

                // Remove any existing download links
                const existingLinks = document.querySelectorAll('.download-link-custom');
                existingLinks.forEach(link => link.remove());

                // Create download link
                const downloadLink = document.createElement('a');
                downloadLink.className = 'btn btn-success btn-lg mt-3 download-link-custom';
                downloadLink.style.display = 'block';
                downloadLink.style.margin = '20px auto';
                downloadLink.style.width = 'fit-content';

                if (data.zip_file) {
                    // Multiple languages - show zip download
                    const zipFileParam = encodeURIComponent(data.zip_file);
                    downloadLink.href = `/translator/api/download/current?zip_file=${zipFileParam}`;
                    downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download All Translations (ZIP)';
                } else {
                    // Single language - show single file download
                    // Get the first (and only) target language from the translation data
                    const targetLanguages = data.languages || [];
                    if (targetLanguages.length > 0) {
                        const langParam = encodeURIComponent(targetLanguages[0]);
                        downloadLink.href = `/translator/api/download/current?lang=${langParam}`;
                    } else {
                        downloadLink.href = '/translator/api/download/current';
                    }
                    downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                }

                // Insert after progress container
                const progressContainer = document.getElementById('progressContainer');
                progressContainer.parentNode.insertBefore(downloadLink, progressContainer.nextSibling);
            } else {
                showAlert('Translation failed: ' + (data.error || 'Unknown error'), 'danger');
            }
        })
        .catch(error => {
            console.error('Translation error:', error);
            translateBtn.disabled = false;
            translateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Start Translation';
            hideProgressBar();
            showAlert('Translation failed due to network error', 'danger');

            if (progressInterval) {
                clearInterval(progressInterval);
            }
        });
    }
});
</script>
{% endblock %}
