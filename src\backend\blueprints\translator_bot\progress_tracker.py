"""
Progress tracking service for translation operations.
"""
import threading
import time
from typing import Dict, Optional
from flask import current_app


class ProgressTracker:
    """Thread-safe progress tracker for translation operations."""
    
    # Class-level storage to persist across instances
    _shared_sessions: Dict[str, Dict] = {}
    _shared_lock = threading.Lock()
    
    def __init__(self):
        # Use class-level storage for persistence across instances
        self._sessions = ProgressTracker._shared_sessions
        self._lock = ProgressTracker._shared_lock
    
    def create_session(self, session_id: str, total_batches: int, details: str = "") -> None:
        """Create a new progress tracking session."""
        with self._lock:
            self._sessions[session_id] = {
                'total': total_batches,
                'completed': 0,
                'details': details,
                'start_time': time.time(),
                'last_updated': time.time()
            }
            current_app.logger.info(f"Created progress session {session_id} with {total_batches} total batches")
    
    def update_progress(self, session_id: str, completed: int = None, increment: int = None, details: str = None) -> None:
        """Update progress for a session."""
        with self._lock:
            if session_id not in self._sessions:
                current_app.logger.warning(f"Progress session {session_id} not found")
                return
            
            session = self._sessions[session_id]
            
            if completed is not None:
                session['completed'] = completed
            elif increment is not None:
                session['completed'] += increment
            
            if details is not None:
                session['details'] = details
            
            session['last_updated'] = time.time()
            
            current_app.logger.debug(f"Updated progress for {session_id}: {session['completed']}/{session['total']}")
    
    def get_progress(self, session_id: str) -> Optional[Dict]:
        """Get current progress for a session."""
        with self._lock:
            if session_id not in self._sessions:
                current_app.logger.warning(f"Progress session {session_id} not found in get_progress")
                return None

            session = self._sessions[session_id].copy()
            elapsed_time = time.time() - session['start_time']
            session['elapsed_time'] = elapsed_time

            current_app.logger.debug(f"Progress for {session_id}: {session['completed']}/{session['total']}")

            # Calculate ETA if we have progress
            if session['completed'] > 0 and session['completed'] < session['total']:
                avg_time_per_batch = elapsed_time / session['completed']
                remaining_batches = session['total'] - session['completed']
                eta_seconds = avg_time_per_batch * remaining_batches
                session['eta_seconds'] = eta_seconds
            else:
                session['eta_seconds'] = None

            # Include completion status
            session['is_completed'] = session.get('is_completed', False)

            return session
    
    def complete_session(self, session_id: str, details: str = "Translation completed") -> None:
        """Mark a session as completed."""
        with self._lock:
            if session_id in self._sessions:
                session = self._sessions[session_id]
                session['completed'] = session['total']
                session['details'] = details
                session['last_updated'] = time.time()
                session['completion_time'] = time.time()  # Track when completed
                session['is_completed'] = True
                current_app.logger.info(f"Completed progress session {session_id}")
    
    def remove_session(self, session_id: str) -> None:
        """Remove a progress session."""
        with self._lock:
            if session_id in self._sessions:
                del self._sessions[session_id]
                current_app.logger.info(f"Removed progress session {session_id}")
    
    def cleanup_old_sessions(self, max_age_seconds: int = 3600, completed_session_retention: int = 300) -> None:
        """Remove sessions older than max_age_seconds. Completed sessions are kept for completed_session_retention seconds."""
        current_time = time.time()
        with self._lock:
            sessions_to_remove = []
            for session_id, session in self._sessions.items():
                # For completed sessions, use a shorter retention time
                if session.get('is_completed', False):
                    completion_time = session.get('completion_time', session['last_updated'])
                    if current_time - completion_time > completed_session_retention:
                        sessions_to_remove.append(session_id)
                # For non-completed sessions, use the standard max_age
                elif current_time - session['last_updated'] > max_age_seconds:
                    sessions_to_remove.append(session_id)
            
            for session_id in sessions_to_remove:
                del self._sessions[session_id]
                current_app.logger.info(f"Cleaned up old progress session {session_id}")
